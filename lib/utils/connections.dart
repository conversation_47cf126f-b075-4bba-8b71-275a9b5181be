String baseurl = '/mobileservices/v2';
String authurl = '$baseurl/auth';
String apiurl = '$baseurl/resources';
/* ------------auth section----------------*/
String validateuserurl = '$authurl/validateuser';
String loginurl = '$authurl/login';
String loginssourl = '$authurl/loginsso';
String changecontexturl = '$authurl/changecontext';
String validateotpurl = '$authurl/validateotp';
String logouturl = '$authurl/logout';

/* -----------------------------------------*/
String mappingurl = '/mapv4services/?context=true&map_locale=en&isApp=true';
String edgeurl = 'https://edgeconfig.tangoanalytics.com/sso';
String userDetailsurl = '/mobilerestservices/resources/rest/logsession';

/* change client context */
String clienturl = '/session/clients';
String brandsurl = '/session/brands';
String buurl = '/session/bu';
String countriesurl = '/session/countries';
String localeurl = '/session/clientcountrylocale';

String acceptusertermsurl = '/acceptuserterms';
/* ------------------- */
String cadViewerurl = '/spacemgmt/';
String documentviewerurl = '/documentviewer/documentviewer.jsp';

//templates
String templateurl = '/mtemplate/1';

String commondataurl = '/common';
String dynamicsearchrul = '/dynamic';
String bundleurl = '$commondataurl/labels';
String entitystatusesurl = '/mentitystatuses/1';
//user entity edit access
String userentityeditaccessurl = '$commondataurl/entityusereditaccess';
//user role access
String userroleaccessurl = '$commondataurl/userroleaccess';
//clear cauche
String refreshurl = '$commondataurl/metadata/clear';
//app bundle
//String labelsurl = '/mlabels/1';
String labelsurl = '$commondataurl/metadata';

//photos
String picturesurl = '$commondataurl/pictures';
String photoaddurl = '$commondataurl/photo/add';
String uploadphotosurl = '$commondataurl/uploadphotos';
String uploadpictureurl = '$commondataurl/uploadpictures';
String editphotourl = '$commondataurl/updatepictureinfo';
String deletepicsurl = '$commondataurl/deletepicsbyid';
//dms
String dmsurl = '/dms';
String adddmsurl = '$dmsurl/add';
String entitydocsurl = '/mentitydocs/1';
String dmsfilesbyidurl = '/mdmsfilesbyid/1';
String dmsfilesurl = '/mdmsfiles/1';
String deletefilesurl = '$dmsurl/deletebyids';
//comments
String commentsurl = '$commondataurl/comments';
String addcommentsurl = '$commondataurl/addcomments';
String imageapiurl = '$commondataurl/image';
String entitycommentsurl = '/mentitycomments/1';
//contatcts
String entitycontactsurl = '/mcommoncontacts/1';

//utils
String adhocnotifyusersurl = '/madhoctasknotifyusers/1';
String dynamicattributesurl = '/dynamicattribute/load/';
//Contacts
String contactsurl = '/mcontacts/1';
String systemusersurl = '/msystemusers/1';
String suppliersitecontactsurl = '/msuppliersitecontacts/1';
String projectsuppliercontactsurl = '/mprojectsuppliercontacts/1';
String projectsuppliersurl = '/mprojectsuppliers/1';

//demographics
String entitydemographicurl = '$commondataurl/demographic';

/*-----------locate enatities------------------------------------------------------*/
String nearestentitiesurl = '/mnearestentities/1';
String locateentitiesurl = '/mlocateentities/1';
//String locateentitiesurl = '/locate/getentities';

/*-----------Project Management---------------------------------------------------------*/

String projectsviewurl = '/mprojects/1';
String documentsurl = '/mdocuments/1';
//budget
String budgetsnapshoturl = '/mbudgetsnapshot/1';
String budgeturl = '/mbudget/1';
String projbaselineversionsurl = '/mprojectbaselineversions/1';
String pohdrurl = '/mpoheader/1';
String cohdrurl = '/mcoheader/1';
String invhdrurl = '/minvheader/1';
String sumbudeturl = '/mbudgettotalsummery/1';

String baselinebudgetbywfurl = '/projectbudget/baselinebudgetbywf';

//milestones
String milestoneurl = '/mmilestones/1';
String updatemilestoneurl = '/milestones/update';
String milestonechecklisturl = '/mmilestonechecklist/1';
String addmilstonechecklisturl = '/milestones/addchecklist';
String updatechecklisturl = '/milestones/updatechecklist';
String milestoneresourcesurl = '/mmilestoneresources/1';
String addmilstoneresourcesurl = '/milestones/addresource';
String updatemilstoneresourcesurl = '/milestones/updateresource';
//punchlist
String punchlisturl = '/mpunchlist/1';
String addpunchlisturl = '/projectpunchlist/add';
String updatepunchlisturl = '/projectpunchlist/update';
String deletepunchlisturl = '/projectpunchlist/delete';
String applyFlagFilterspunchlisturl = '/projectpunchlist/applyFlagFilters';
String generateitemsurl = '/projectpunchlist/generateitems';
//adhoctasks
String adhoctaskslisturl = '/madhoctasks/1';
String generateadhocfromtemplateurl = '/adhoctasks/generateadhoctasks';
String adhoctemplatesurl = '/madhoctemplates/1';
String createadhoctaskurl = '/adhoctasks/create';
String updateadhoctaskurl = '/adhoctasks/update';
String saveadhocnotifiusersurl = '/adhoctasks/saveadhoctasknotificationusers';
String getadhoctaskidurl = '/adhoctasks/getadhoctaskid';
String saveadhoctaskdocsurl = '/adhoctasks/saveadhoctaskdocs';

//project status Reports
String statusreportshdrurl = '/mprojectstatusheader/1';
String projectstatusreportcreateurl = '/projectstatusreport/create';
String projectstatusreportupdateurl = '/projectstatusreport/update';
String projectstatusreportdeleteurl = '/projectstatusreport/delete';
String projectstatusreportupdatedetailsurl = '/projectstatusreport/updatedetail';
String projectstatusreportdetailsurl = '/mprojectstatusreportdetails/1';

//entity meeting minutes
String meetingminutesurl = '/mmeetingminutes/1';
String meetingattendeesurl = '/mmeetingattendees/1';
String meetingfollowupsurl = '/mmeetingfollowups/1';
String createmeetingminuteurl = '/meetingminutes/create';
String updatemeetingminuteurl = '/meetingminutes/update';
String createmeetingfollowupurl = '/meetingminutes/createfollowup';
String saveattendeesurl = '/meetingminutes/saveattendees';
/*-----------Supplier Management---------------------------------------------------------*/
String suppliersearchviewurl = '/msuppliersearchview/1';
String suppliersitesearchviewurl = '/msuppliersitesearchview/1';
String suppliersurl = '/msuppliers/1';

/*-----------Site Management---------------------------------------------------------*/
String sitesearchviewurl = '/msiteviewsearch/1';
String sitesbvourl = '/msitesbvo/1';
String siteinfotabattributesurl = '/msiteinfotabattributes/1';

String savesiterecordurl = '/site/save';
//swot
String savesiteswoturl = '/siteswot/save';

/*-----------Target Management---------------------------------------------------------*/
String targetsearchviewurl = '/mtargetviewsearch/1';
String targetbvourl = '/mtargetbvo/1';
String saveattributesurl = '/target/saveattributes';
String fieldvalidationdataurl = '/target/getfieldvalidationitems';
String updatefieldvalidationurl = '/target/updatefieldvalidation';

/*-------------------------------*/

/*-----------Location Entity Management---------------------------------------------------------*/
String locationentityviewurl = '/mlocationentityview/1';
/*----------------------------------------------------------------------------------------------*/

/*-----------Contracts---------------------------------------------------------*/
//---------Lease
String leasehomesearchviewurl = '/mleasehomesearchview/1';
String leasebatchrentpaysummeryurl = '/mleasebatchrentpaysummery/1';
String leasebatchrentbillsummeryurl = '/mleasebatchrentbillsummary/1';
String leasebatchrentinvsummeryurl = '/mleasebatchrentinvsummary/1';
String leasepaymentsforbatchrenturl = '/mleasepaymentsforbatchrent/1';
String leasereceivablesforbatchrenturl = '/mleasereceivablesforbatchrent/1';
/*----------------------------------------------------------------------------------------------*/
//-------------workflow
String muserpendingtaskcounturl = '/muserpendingtaskcount/1'; //home page approvals badge count
String userpendingtasksurl = '/musertasks/1';
String projecttasksurl = '/muserprojecttasks/1';
String budgettasksurl = '/muserbudgettasks/1';
String budgetcotasksurl = '/muserbudgetcotasks/1'; //BUDGET CO
String potasksurl = '/muserpotasks/1';
String cotasksurl = '/musercotasks/1';
String ewatasksurl = '/muserewatasks/1';
String invoicetasksurl = '/muserinvoicetasks/1';
String documenttasksurl = '/muserdoctasks/1';

String sitetaskscounturl = '/msitetaskscount/1';
String sitetaskslisturl = '/msitetaskslist/1';

String leasetasksurl = '/muserleasetasks/1';
String leaseotptasksurl = '/muserleaseotptasks/1';
String userleasebatchrenttasksurl = '/muserleasebatchrenttasks/1'; //LEASE BATCH-RENT
String userleaserecurringcosttasksurl = '/muserleaserecurringcosttasks/1';
String wfhistoryurl = '/mworkflowhistory/1';
String submitwftaskurl = '/workflow/submittask';

String userprojecttaskcounturl = '/muserprojecttaskcount/1';
String userprojectbudgettaskcounturl = '/muserprojectbudgettaskcount/1';
String userprojectbudgetcotaskcounturl = '/muserbudgetcotaskcount/1'; //BUDGET CHANGE REQ
String userpotaskcounturl = '/muserpotaskcount/1';
String usercotaskcounturl = '/musercotaskcount/1';
String userinvoicetaskcounturl = '/muserinvoicetaskcount/1';
String userewataskcounturl = '/muserewataskcount/1';

String userdoctaskcounturl = '/muserdoctaskcount/1';
String userleasetaskcounturl = '/muserleasetaskcount/1';
String userleaseotptaskcounturl = '/muserleaseotptaskcount/1';
String userleasebatchrenttaskcounturl = '/muserleasebatchrenttaskcount/1'; //LEASE BATCH-RENT
String userleaserecurringcosttaskcounturl = '/muserleaserecurringcosttaskcount/1';
/*-------------------------------*/
String projectsurl = '/projectslist';
String reservationsurl = '/reservations/search';
String reservationsaddurl = '/reservations/add';
String reservationsupdateurl = '/reservations/update';
String reservationassigneeurl = '/reservations/assignees';
String reservationupdatestatusurl = '/reservations/updatestatus';
String reservationupdatecommentsurl = '/reservations/updatecomments';
/*-----------new Reservations----------------------------------------------------------------------------------*/
String reservationlisturl = '/mreservationlist/1';
String reservpropertylovurl = '/mreservpropertylov/1';
String reservbuildinglovurl = '/mreservbuildinglov/1';
String reservspaceslovurl = '/mreservspaceslov/1';

/*-buildings-------------------------------------*/
String buildingsearchviewurl = '/mbuildingsearchview/1';
String buildingentityurl = '/mbuildingentity/1';
/*-Properties-------------------------------------*/
String propertysearchviewurl = '/mpropertyview/1';

/*-programs-------------------------------------*/
String programsearchviewurl = '/mprogramsearchview/1';
String programprojectsurl = '/mprogramprojects/1';
//---------------------------------------

String propertylovurl = '/buildings/propertyinfo';
String propertybuildingfloorurl = '/propertyinfo/propertybuildingfloorinfo';
String personListurl = '/person/list';
String recentpersonListurl = '/person/recentlist';
String recentreservationslocurl = '/reservations/recentreservations';
String personspacelovurl = '/person/spacelist';
String findspacesurl = '/mfindspaces/1';
String findpersonsurl = '/mfindpersons/1';
String personurl = '/person';
String spaceurl = '/spaces';
//String spacelovurl = spaceurl+ '/lovlist';
String macrequesturl = '/spaces/macrequests';
String findCount = '/findcount/';
String personuidurl = '$personurl/uid/';

//service request
String servicerequestsearchurl = '/mservicerequestsearch/1';
String servicerequesturl = '/servicerequest';
String getsrlocationsurl = '$servicerequesturl/locations';
String srfloorsurl = '/msrfloors/1';
String srspacesurl = '/msrspaces/1';
String srpersonprimelocurl = '/msrpersonprimaryloc/1';
String getservicerequestidurl = '$servicerequesturl/srid';
String saveservicerequesturl = '$servicerequesturl/saveservicerequest';
String srassetnamelovurl = '/mservicerequestassetnamelov/1';

//MAC
String macrequestsearchurl = '/mmacrequestsearch/1';
