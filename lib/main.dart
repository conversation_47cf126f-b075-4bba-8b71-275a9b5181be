import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'common/widgets/login_session.dart';
import 'screens/projectmanagement/project/project_search_screen.dart';
import 'screens/space/assigneduser_search.dart';
import 'screens/space/find/find_detail_screen.dart';
import 'screens/space/property_search.dart';
import 'screens/space/space_search.dart';

import 'providers/macrequests_provider.dart';
import 'providers/reservation_provider.dart';
import 'providers/find_provider.dart';
import 'screens/settings_screen.dart';
import 'screens/space/macrequest/macrequest_detail_screen.dart';
import 'screens/space/macrequest/macrequest_search_screen.dart';
import 'screens/space/reservations/reservations_search_screen.dart';
import 'screens/space/find/find_search_screen.dart';

import 'screens/home/<USER>';
import 'screens/login_screen.dart';
import 'screens/space/reservations/reservations_detail_screen.dart';
import 'screens/ta_admin/change_context_screen.dart';
import 'utils/common_utils.dart';
import 'utils/constvariables.dart';
import 'utils/preferences_utils.dart';
import 'package:get/get.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'utils/themedata.dart';

void main() async {
  debugPrint = (String? message, {int? wrapWidth}) {};

  //setUrlStrategy(PathUrlStrategy());
  bool isLogged = false;
  Timer timer;

  WidgetsFlutterBinding.ensureInitialized();
  await SharedPrefUtils.init();
  tz.initializeTimeZones();

  String? sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
  debugPrint(sessionid);

  if (sessionid != null) {
    String? expiryDate = await SharedPrefUtils.readPrefStr('ExpiryDate');
    if (expiryDate != null) {
      final expiryDateVal = DateTime.parse(await SharedPrefUtils.readPrefStr('ExpiryDate'));

      if (expiryDateVal.isBefore(DateTime.now())) {
        debugPrint('Expired');
        SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, "");
        SharedPrefUtils.saveStr(ConstHelper.userNamevar, "");
        SharedPrefUtils.saveStr(ConstHelper.authVar, "");
        isLogged = false;
      } else {
        isLogged = true;
      }
    }
  }

  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.dumpErrorToConsole(details);
  };

  List<SingleChildWidget> providers = [
    ChangeNotifierProvider<ReservationProvider>(create: (_) => ReservationProvider()),
    ChangeNotifierProvider<MacRequestProvider>(create: (_) => MacRequestProvider()),
    ChangeNotifierProvider<FindProvider>(create: (_) => FindProvider()),
  ];

  // mount and run the flutter app
  WidgetsFlutterBinding.ensureInitialized();
  // SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]).then((_) {

  runApp(
    MultiProvider(
      providers: providers,
      child: TaApp(),
    ),
  );
  // });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      theme: ThemeData(primarySwatch: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), useMaterial3: false),
      initialRoute: '/',
      routes: {'/': (ctx) => LoginPage(), SettingsForm.routName: (ctx) => SettingsForm()},
    );
  }
}

class TaApp extends StatefulWidget {
  const TaApp({super.key});

  @override
  _TaAppState createState() => _TaAppState();
}

class _TaAppState extends State<TaApp> {
  final _navigator = GlobalKey<NavigatorState>();
  bool isLogged = false;
  //static Timer _sessionTimer;

  late Timer _timer;
  bool forceLogout = false;
  // final navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();

    _initializeTimer();
  }

  void _initializeTimer() {
    _timer = Timer.periodic(Duration(minutes: ConstHelper.APP_TIMEOUT), (_) => _logOutUser());
  }

  void _logOutUser() {
    // Log out the user if they're logged in, then cancel the timer.
    // You'll have to make sure to cancel the timer if the user manually logs out
    //   and to call _initializeTimer once the user logs in
    _timer.cancel();
    setState(() {
      forceLogout = true;
    });
  }

  // You'll probably want to wrap this function in a debounce
  void _handleUserInteraction([_]) {
    //print("_handleUserInteraction");
    _timer.cancel();
    _initializeTimer();
  }

  void navToLoginPage(BuildContext context) {
    //Clear all pref's
    SharedPrefUtils.clear();

    _navigator.currentState!
        .pushAndRemoveUntil(MaterialPageRoute(builder: (context) => LoginPage()), (Route<dynamic> route) => false);
  }

  @override
  Widget build(BuildContext context) {
    if (forceLogout) {
      debugPrint("ForceLogout is $forceLogout");
      navToLoginPage(context);
    }
    return GestureDetector(
      onTap: _handleUserInteraction,
      onPanDown: _handleUserInteraction,
      onScaleStart: _handleUserInteraction,
      child: GetMaterialApp(
        navigatorKey: _navigator,
        debugShowCheckedModeBanner: false,
        theme: TaLightTheme(),
        // theme: ThemeData(primarySwatch: CommonUtils.createMaterialColor(Color(0XFFb10c00)),),
        //getPages: MainPgUtils.pages,
        initialRoute: '/',
        routes: {
          '/': (ctx) => isLogged == true ? HomeScreen() : LoginPage(),
          HomeScreen.routName: (ctx) => HomeScreen(),
          SettingsForm.routName: (ctx) => SettingsForm(),
          TaChange.routName: (ctx) => TaChange(),
          LoginPage.routName: (ctx) => LoginPage(),
          ProjectSearch.routName: (ctx) => ProjectSearch(),
          ReservationsScreen.routName: (ctx) => ReservationsScreen(),
          ReservationAdd.routName: (ctx) => ReservationAdd(),
          MacRequestSearchScreen.routName: (ctx) => MacRequestSearchScreen(),
          MacRequestDetail.routName: (ctx) => MacRequestDetail(),
          FindScreen.routName: (ctx) => FindScreen(),
          PropertyScreen.routName: (ctx) => PropertyScreen(),
          SpaceSearch.routName: (ctx) => SpaceSearch(),
          AssignedUserSearch.routName: (ctx) => AssignedUserSearch(),
          FindDetails.routName: (ctx) => FindDetails(),
          LoginSessionImpl.routName: (ctx) => LoginSessionImpl('')
        },
      ),
    );
  }
}
