plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty("flutter.sdk")
if (flutterRoot == null) {
    throw new FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode") ?: "2"
def flutterVersionName = localProperties.getProperty("flutter.versionName") ?: "1.0"

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.tango.tangoworkplace"
    compileSdkVersion 35

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
    }

    lintOptions {
        disable "InvalidPackage"
        disable "Instantiatable"
        checkReleaseBuilds false
        abortOnError false
    }

    defaultConfig {
        applicationId "com.tango.tangoworkplace"
        minSdkVersion 31
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties["keyAlias"]
            keyPassword keystoreProperties["keyPassword"]
            storeFile keystoreProperties["storeFile"] ? file(keystoreProperties["storeFile"]) : null
            storePassword keystoreProperties["storePassword"]
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.20" // Updated to match Kotlin plugin version
    implementation "androidx.core:core-ktx:1.12.0"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation "androidx.camera:camera-core:1.4.1"
    implementation "androidx.camera:camera-camera2:1.4.1"
    implementation "androidx.camera:camera-video:1.4.1"
    implementation "com.google.errorprone:error_prone_annotations:2.36.0"
}

subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
            project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 35
            }
        }
    }
}

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(":app")
}